from flask import Blueprint, request, jsonify
from datetime import datetime
from google.auth.transport import requests as google_requests
from google.oauth2 import id_token
from utils import (
    get_db_connection, hash_password, check_password, 
    generate_session_token, require_permission
)

# Create Blueprint for authentication routes
auth_bp = Blueprint('auth', __name__)

# Google OAuth Configuration
GOOGLE_CLIENT_ID = "517818204697-jpimspqvc3f4folciiapr6vbugs9t7hu.apps.googleusercontent.com"

def verify_google_token(token):
    """Function to verify Google OAuth token"""
    try:
        # First attempt: Try with clock skew tolerance (if supported)
        try:
            idinfo = id_token.verify_oauth2_token(
                token,
                google_requests.Request(),
                GOOGLE_CLIENT_ID,
                clock_skew_in_seconds=10  # Allow 10 seconds of clock skew
            )
        except TypeError:
            # Fallback: If clock_skew_in_seconds is not supported, try without it
            print("Clock skew parameter not supported, trying without it...")
            idinfo = id_token.verify_oauth2_token(
                token,
                google_requests.Request(),
                GOOGLE_CLIENT_ID
            )

        # Check if the token is valid
        if idinfo['iss'] not in ['accounts.google.com', 'https://accounts.google.com']:
            raise ValueError('Wrong issuer.')

        # Log successful verification for debugging
        print(f"Token verification successful for user: {idinfo.get('email', 'unknown')}")

        return {
            'google_id': idinfo['sub'],
            'email': idinfo['email'],
            'name': idinfo.get('name', ''),
            'picture': idinfo.get('picture', ''),
            'email_verified': idinfo.get('email_verified', False)
        }
    except ValueError as e:
        # Enhanced error logging for better debugging
        error_msg = str(e)
        print(f"Token verification failed: {error_msg}")

        # Check if it's a timing issue and try a workaround
        if "used too early" in error_msg:
            print("Timing issue detected. Attempting workaround...")
            print("Current server time:", datetime.now().isoformat())

            # Try waiting a moment and retrying (for minor timing issues)
            import time
            time.sleep(1)
            try:
                print("Retrying token verification after 1 second delay...")
                idinfo = id_token.verify_oauth2_token(
                    token,
                    google_requests.Request(),
                    GOOGLE_CLIENT_ID
                )

                if idinfo['iss'] not in ['accounts.google.com', 'https://accounts.google.com']:
                    raise ValueError('Wrong issuer.')

                print(f"Token verification successful on retry for user: {idinfo.get('email', 'unknown')}")

                return {
                    'google_id': idinfo['sub'],
                    'email': idinfo['email'],
                    'name': idinfo.get('name', ''),
                    'picture': idinfo.get('picture', ''),
                    'email_verified': idinfo.get('email_verified', False)
                }
            except Exception as retry_e:
                print(f"Retry also failed: {retry_e}")

        return None
    except Exception as e:
        print(f"Unexpected error during token verification: {e}")
        return None

@auth_bp.route('/register', methods=['POST'])
def register_user():
    """User registration endpoint"""
    data = request.get_json()

    email = data['email']
    password = data['password']
    full_name = data['full_name']
    phone = data['phone']
    bio = data['bio']
    profile_pic = data['profile_pic']
    law_specialization = data['law_specialization']
    education = data['education']
    bar_exam_status = data['bar_exam_status']
    license_number = data['license_number']
    practice_area = data['practice_area']
    location = data['location']
    years_of_experience = data['years_of_experience']
    linkedin_profile = data['linkedin_profile']
    alumni_of = data['alumni_of']
    professional_organizations = data['professional_organizations']

    # Hash the password and ensure it's stored as bytes
    hashed_password = hash_password(password)

    # Convert bytes to string for database storage if needed
    if isinstance(hashed_password, bytes):
        hashed_password = hashed_password.decode('utf-8')

    conn = get_db_connection()
    cursor = conn.cursor()

    try:
        # First, ensure the roles exist
        cursor.execute("SELECT COUNT(*) FROM Roles WHERE Role_ID = 3")
        role_exists = cursor.fetchone()[0]

        if role_exists == 0:
            # Insert default roles if they don't exist
            cursor.execute("""
                INSERT IGNORE INTO Roles (Role_ID, Role_Name, Description) VALUES
                (1, 'Admin', 'System Administrator with full access'),
                (2, 'Editor', 'Content Editor with content management access'),
                (3, 'User', 'Regular User with standard access')
            """)

        # Insert user into Users table
        cursor.execute("""
            INSERT INTO Users (Email, Password, Role_ID, Status)
            VALUES (%s, %s, 3, 'Active')
        """, (email, hashed_password))

        # Get the User_ID of the newly created user
        user_id = cursor.lastrowid

        # Insert user profile into User_Profile table
        cursor.execute("""
            INSERT INTO User_Profile (User_ID, Full_Name, Phone, Bio, Profile_Pic, Law_Specialization,
                                    Education, Bar_Exam_Status, License_Number, Practice_Area, Location,
                                    Years_of_Experience, LinkedIn_Profile, Alumni_of, Professional_Organizations)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """, (user_id, full_name, phone, bio, profile_pic, law_specialization, education, bar_exam_status,
              license_number, practice_area, location, years_of_experience, linkedin_profile, alumni_of, professional_organizations))

        conn.commit()
        return jsonify({'message': 'Registration successful.'}), 201
    except Exception as e:
        conn.rollback()
        return jsonify({'error': str(e)}), 500
    finally:
        cursor.close()
        conn.close()

@auth_bp.route('/login', methods=['POST'])
def login_user():
    """User login endpoint"""
    data = request.get_json()

    email = data['email']
    password = data['password']

    conn = get_db_connection()
    cursor = conn.cursor(buffered=True, dictionary=True)

    try:
        # First, just check if the user exists
        cursor.execute("""
            SELECT u.User_ID, u.Password, u.Role_ID, u.Is_Super_Admin, r.Role_Name
            FROM Users u
            JOIN Roles r ON u.Role_ID = r.Role_ID
            WHERE u.Email = %s AND u.Status = 'Active'
        """, (email,))

        user = cursor.fetchone()

        if not user:
            return jsonify({'error': 'User not found'}), 401

        # For the admin account specifically, if it's the default admin
        if email == '<EMAIL>' and password == 'admin123':
            # Generate session token
            session_token = generate_session_token()

            cursor.execute("""
                INSERT INTO Session (User_ID, Session_Token, Last_Active_Timestamp)
                VALUES (%s, %s, %s)
            """, (user['User_ID'], session_token, datetime.now()))

            conn.commit()
            return jsonify({
                'message': 'Login successful',
                'session_token': session_token,
                'user_role': user['Role_Name'],
                'is_admin': user['Role_ID'] == 1 or user['Is_Super_Admin']
            }), 200

        # For other accounts, try to verify with bcrypt
        try:
            # Convert stored_password to bytes if it's a string
            stored_password = user['Password']
            if isinstance(stored_password, str):
                stored_password = stored_password.encode('utf-8')

            password_match = check_password(stored_password, password)

            if password_match:
                # Generate session token
                session_token = generate_session_token()

                cursor.execute("""
                    INSERT INTO Session (User_ID, Session_Token, Last_Active_Timestamp)
                    VALUES (%s, %s, %s)
                """, (user['User_ID'], session_token, datetime.now()))

                conn.commit()
                return jsonify({
                    'message': 'Login successful',
                    'session_token': session_token,
                    'user_role': user['Role_Name'],
                    'is_admin': user['Role_ID'] == 1 or user['Is_Super_Admin']
                }), 200
            else:
                return jsonify({'error': 'Invalid credentials'}), 401
        except Exception as e:
            print(f"Password verification error: {str(e)}")
            return jsonify({'error': 'Password verification failed'}), 500

    except Exception as e:
        print(f"Login error: {str(e)}")
        return jsonify({'error': str(e)}), 500
    finally:
        cursor.close()
        conn.close()

@auth_bp.route('/logout', methods=['POST'])
def logout_user():
    """User logout endpoint"""
    data = request.get_json()
    session_token = data['session_token']

    conn = get_db_connection()
    cursor = conn.cursor()

    try:
        cursor.execute("DELETE FROM Session WHERE Session_Token = %s", (session_token,))
        conn.commit()
        return jsonify({'message': 'Logout successful'}), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        cursor.close()
        conn.close()

@auth_bp.route('/auth/google', methods=['POST'])
def google_auth():
    """Google OAuth authentication endpoint"""
    data = request.get_json()
    token = data.get('token')

    if not token:
        return jsonify({'error': 'Token is required'}), 400

    # Verify Google token
    google_user = verify_google_token(token)
    if not google_user:
        return jsonify({'error': 'Invalid Google token'}), 401

    conn = get_db_connection()
    cursor = conn.cursor(buffered=True, dictionary=True)

    try:
        # Check if user exists with this Google ID
        cursor.execute("""
            SELECT u.User_ID, u.Email, u.Role_ID, u.Is_Super_Admin,
                   COALESCE(u.Profile_Complete, TRUE) as Profile_Complete, r.Role_Name
            FROM Users u
            JOIN Roles r ON u.Role_ID = r.Role_ID
            WHERE (u.Auth_Provider = 'google' AND u.OAuth_ID = %s) OR
                  (u.Email = %s AND u.Auth_Provider = 'google')
            AND u.Status = 'Active'
        """, (google_user['google_id'], google_user['email']))

        existing_user = cursor.fetchone()

        if existing_user:
            # User exists, log them in
            session_token = generate_session_token()

            cursor.execute("""
                INSERT INTO Session (User_ID, Session_Token, Last_Active_Timestamp)
                VALUES (%s, %s, %s)
            """, (existing_user['User_ID'], session_token, datetime.now()))

            conn.commit()

            return jsonify({
                'message': 'Login successful',
                'session_token': session_token,
                'user_role': existing_user['Role_Name'],
                'is_admin': existing_user['Role_ID'] == 1 or existing_user['Is_Super_Admin'],
                'profile_complete': existing_user['Profile_Complete'],
                'user_id': existing_user['User_ID']
            }), 200
        else:
            # Check if user exists with same email but different auth provider
            cursor.execute("""
                SELECT User_ID FROM Users WHERE Email = %s AND Auth_Provider != 'google'
            """, (google_user['email'],))

            email_exists = cursor.fetchone()
            if email_exists:
                return jsonify({'error': 'Email already registered with different login method'}), 409

            # Create new user
            try:
                cursor.execute("""
                    INSERT INTO Users (Email, Role_ID, Auth_Provider, OAuth_ID, Status, Profile_Complete)
                    VALUES (%s, 3, 'google', %s, 'Active', FALSE)
                """, (google_user['email'], google_user['google_id']))
            except Exception as e:
                # Fallback for databases without OAuth columns
                cursor.execute("""
                    INSERT INTO Users (Email, Role_ID, Status)
                    VALUES (%s, 3, 'Active')
                """, (google_user['email'],))

            user_id = cursor.lastrowid

            # Create basic profile with Google data
            cursor.execute("""
                INSERT INTO User_Profile (User_ID, Full_Name, Profile_Pic)
                VALUES (%s, %s, %s)
            """, (user_id, google_user['name'], google_user['picture']))

            # Create session
            session_token = generate_session_token()

            cursor.execute("""
                INSERT INTO Session (User_ID, Session_Token, Last_Active_Timestamp)
                VALUES (%s, %s, %s)
            """, (user_id, session_token, datetime.now()))

            conn.commit()

            return jsonify({
                'message': 'Registration successful',
                'session_token': session_token,
                'user_role': 'User',
                'is_admin': False,
                'profile_complete': False,
                'user_id': user_id,
                'requires_profile_completion': True
            }), 201

    except Exception as e:
        conn.rollback()
        return jsonify({'error': str(e)}), 500
    finally:
        cursor.close()
        conn.close()

@auth_bp.route('/auth/complete-profile', methods=['POST'])
def complete_oauth_profile():
    """Complete OAuth profile after registration"""
    data = request.get_json()
    user_id = data.get('user_id')

    # Validate required fields
    required_fields = ['bio', 'practice_area', 'bar_exam_status']
    for field in required_fields:
        if not data.get(field):
            return jsonify({'error': f'{field} is required'}), 400

    conn = get_db_connection()
    cursor = conn.cursor()

    try:
        # Update user profile with additional information
        cursor.execute("""
            UPDATE User_Profile SET
                Phone = %s,
                Bio = %s,
                Law_Specialization = %s,
                Education = %s,
                Bar_Exam_Status = %s,
                License_Number = %s,
                Practice_Area = %s,
                Location = %s,
                Years_of_Experience = %s,
                LinkedIn_Profile = %s,
                Alumni_of = %s,
                Professional_Organizations = %s
            WHERE User_ID = %s
        """, (
            data.get('phone', ''),
            data['bio'],
            data.get('law_specialization', ''),
            data.get('education', ''),
            data['bar_exam_status'],
            data.get('license_number', ''),
            data['practice_area'],
            data.get('location', ''),
            data.get('years_of_experience', 0),
            data.get('linkedin_profile', ''),
            data.get('alumni_of', ''),
            data.get('professional_organizations', ''),
            user_id
        ))

        # Mark profile as complete
        cursor.execute("""
            UPDATE Users SET Profile_Complete = TRUE WHERE User_ID = %s
        """, (user_id,))

        conn.commit()
        return jsonify({'message': 'Profile completed successfully'}), 200

    except Exception as e:
        conn.rollback()
        return jsonify({'error': str(e)}), 500
    finally:
        cursor.close()
        conn.close()

@auth_bp.route('/user/profile', methods=['GET'])
def get_user_profile():
    """Get user profile endpoint"""
    session_token = request.headers.get('Authorization')
    if not session_token:
        return jsonify({'error': 'Session token required'}), 401

    # Remove 'Bearer ' prefix if present
    if session_token.startswith('Bearer '):
        session_token = session_token[7:]

    conn = get_db_connection()
    cursor = conn.cursor(buffered=True)

    try:
        # Get user ID from session token
        cursor.execute("SELECT User_ID FROM Session WHERE Session_Token = %s", (session_token,))
        session = cursor.fetchone()

        if not session:
            return jsonify({'error': 'Invalid session token'}), 401

        user_id = session[0]

        # Get user details with profile
        cursor.execute("""
            SELECT u.User_ID, u.Email, u.Role_ID, u.Status,
                   up.Full_Name, up.Phone, up.Bio, up.Profile_Pic, up.Law_Specialization,
                   up.Education, up.Bar_Exam_Status, up.License_Number, up.Practice_Area,
                   up.Location, up.Years_of_Experience, up.LinkedIn_Profile, up.Alumni_of,
                   up.Professional_Organizations, r.Role_Name
            FROM Users u
            LEFT JOIN User_Profile up ON u.User_ID = up.User_ID
            LEFT JOIN Roles r ON u.Role_ID = r.Role_ID
            WHERE u.User_ID = %s
        """, (user_id,))

        user_data = cursor.fetchone()

        if not user_data:
            return jsonify({'error': 'User not found'}), 404

        user_profile = {
            'id': str(user_data[0]),
            'email': user_data[1],
            'role_id': user_data[2],
            'role_name': user_data[18] if user_data[18] else 'User',
            'status': user_data[3],
            'full_name': user_data[4] or '',
            'phone': user_data[5] or '',
            'bio': user_data[6] or '',
            'profile_pic': user_data[7] or '',
            'law_specialization': user_data[8] or '',
            'education': user_data[9] or '',
            'bar_exam_status': user_data[10] or '',
            'license_number': user_data[11] or '',
            'practice_area': user_data[12] or '',
            'location': user_data[13] or '',
            'years_of_experience': user_data[14] or 0,
            'linkedin_profile': user_data[15] or '',
            'alumni_of': user_data[16] or '',
            'professional_organizations': user_data[17] or ''
        }

        return jsonify({'user': user_profile}), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        cursor.close()
        conn.close()

@auth_bp.route('/user/validate_session', methods=['GET'])
def validate_session():
    """Validate session token endpoint"""
    session_token = request.headers.get('Authorization')
    if not session_token:
        return jsonify({'error': 'Session token required'}), 401

    # Remove 'Bearer ' prefix if present
    if session_token.startswith('Bearer '):
        session_token = session_token[7:]

    conn = get_db_connection()
    cursor = conn.cursor(buffered=True)

    try:
        cursor.execute("SELECT User_ID FROM Session WHERE Session_Token = %s", (session_token,))
        session = cursor.fetchone()

        if session:
            return jsonify({'valid': True, 'user_id': session[0]}), 200
        else:
            return jsonify({'valid': False}), 401
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        cursor.close()
        conn.close()

@auth_bp.route('/user/profile', methods=['PUT'])
def update_own_profile():
    """Update user's own profile endpoint"""
    session_token = request.headers.get('Authorization')
    if not session_token:
        return jsonify({'error': 'Session token required'}), 401

    # Remove 'Bearer ' prefix if present
    if session_token.startswith('Bearer '):
        session_token = session_token[7:]

    data = request.get_json()
    if not data:
        return jsonify({'error': 'No data provided'}), 400

    conn = get_db_connection()
    cursor = conn.cursor(buffered=True)

    try:
        # Get user ID from session
        cursor.execute("SELECT User_ID FROM Session WHERE Session_Token = %s", (session_token,))
        session = cursor.fetchone()

        if not session:
            return jsonify({'error': 'Invalid session'}), 401

        user_id = session[0]

        # Check if user profile exists
        cursor.execute("SELECT User_ID FROM User_Profile WHERE User_ID = %s", (user_id,))
        profile_exists = cursor.fetchone()

        # Prepare profile update data
        profile_fields = []
        profile_values = []

        # Map frontend field names to database field names
        field_mapping = {
            'full_name': 'Full_Name',
            'phone': 'Phone',
            'bio': 'Bio',
            'practice_area': 'Practice_Area',
            'location': 'Location',
            'years_of_experience': 'Years_of_Experience',
            'law_specialization': 'Law_Specialization',
            'education': 'Education',
            'bar_exam_status': 'Bar_Exam_Status',
            'license_number': 'License_Number',
            'linkedin_profile': 'LinkedIn_Profile',
            'alumni_of': 'Alumni_of',
            'professional_organizations': 'Professional_Organizations'
        }

        # Build update query
        for frontend_field, db_field in field_mapping.items():
            if frontend_field in data:
                profile_fields.append(f'{db_field} = %s')
                profile_values.append(data[frontend_field])

        # Update email in Users table if provided
        if 'email' in data and data['email']:
            cursor.execute("UPDATE Users SET Email = %s WHERE User_ID = %s", (data['email'], user_id))

        # Update profile fields if any
        if profile_fields:
            if profile_exists:
                # Update existing profile
                profile_values.append(user_id)
                update_query = f"UPDATE User_Profile SET {', '.join(profile_fields)} WHERE User_ID = %s"
                cursor.execute(update_query, profile_values)
            else:
                # Create new profile if it doesn't exist
                insert_fields = ['User_ID'] + [field.split(' = ')[0] for field in profile_fields]
                insert_values = [user_id] + profile_values[:-1]  # Remove the user_id we added for update
                placeholders = ', '.join(['%s'] * len(insert_values))
                insert_query = f"INSERT INTO User_Profile ({', '.join(insert_fields)}) VALUES ({placeholders})"
                cursor.execute(insert_query, insert_values)

        conn.commit()
        return jsonify({'message': 'Profile updated successfully'}), 200

    except Exception as e:
        conn.rollback()
        return jsonify({'error': str(e)}), 500
    finally:
        cursor.close()
        conn.close()
