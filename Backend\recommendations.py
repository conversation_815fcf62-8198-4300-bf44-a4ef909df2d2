from flask import Blueprint, request, jsonify
from utils import get_db_connection, require_permission

# Create Blueprint for recommendation routes
recommendations_bp = Blueprint('recommendations', __name__)

def calculate_engagement_score(views, likes, shares, comments):
    """Calculate engagement score based on content metrics"""
    # Weighted scoring system
    return (views * 0.4 + likes * 0.3 + shares * 0.2 + comments * 0.1)

def get_user_practice_area(user_id):
    """Get user's practice area for personalized recommendations"""
    try:
        connection = get_db_connection()
        cursor = connection.cursor(dictionary=True)
        
        cursor.execute("""
            SELECT Practice_Area FROM User_Profile WHERE User_ID = %s
        """, (user_id,))
        
        result = cursor.fetchone()
        cursor.close()
        connection.close()
        
        return result['Practice_Area'] if result and result['Practice_Area'] else None
    except Exception as e:
        return None

def get_content_recommendations(content_type='all', user_id=None, limit=10, sort_by='engagement'):
    """
    Get content recommendations based on various criteria
    
    Args:
        content_type: 'blog', 'research_paper', 'note', or 'all'
        user_id: User ID for personalized recommendations
        limit: Number of recommendations to return
        sort_by: 'engagement', 'recent', 'popular', 'trending'
    """
    try:
        connection = get_db_connection()
        cursor = connection.cursor(dictionary=True)
        
        # Base query for content recommendations
        base_query = """
            SELECT 
                c.Content_ID as content_id,
                c.Title as title,
                c.Summary as summary,
                c.Content_Type as content_type,
                c.Created_At as created_at,
                c.Is_Featured as is_featured,
                COALESCE(cm.Views, 0) as views,
                COALESCE(cm.Likes, 0) as likes,
                COALESCE(cm.Shares, 0) as shares,
                COALESCE(cm.Comments_Count, 0) as comments,
                up.Full_Name as author_name,
                up.Practice_Area as author_practice_area
            FROM Content c
            LEFT JOIN Content_Metrics cm ON c.Content_ID = cm.Content_ID
            LEFT JOIN User_Profile up ON c.User_ID = up.User_ID
            WHERE c.Status = 'Active'
        """
        
        params = []
        
        # Filter by content type if specified
        if content_type != 'all':
            if content_type == 'blog':
                base_query += " AND c.Content_Type = 'Blog_Post'"
            elif content_type == 'research_paper':
                base_query += " AND c.Content_Type = 'Research_Paper'"
            elif content_type == 'note':
                base_query += " AND c.Content_Type = 'Note'"
        
        # Add personalization based on user's practice area
        user_practice_area = None
        if user_id:
            user_practice_area = get_user_practice_area(user_id)
            if user_practice_area:
                # Boost content from same practice area
                base_query += " AND (up.Practice_Area = %s OR up.Practice_Area IS NULL)"
                params.append(user_practice_area)
        
        # Add sorting
        if sort_by == 'engagement':
            base_query += """
                ORDER BY 
                    (COALESCE(cm.Views, 0) * 0.4 + 
                     COALESCE(cm.Likes, 0) * 0.3 + 
                     COALESCE(cm.Shares, 0) * 0.2 + 
                     COALESCE(cm.Comments_Count, 0) * 0.1) DESC,
                    c.Created_At DESC
            """
        elif sort_by == 'recent':
            base_query += " ORDER BY c.Created_At DESC"
        elif sort_by == 'popular':
            base_query += " ORDER BY COALESCE(cm.Views, 0) DESC, c.Created_At DESC"
        elif sort_by == 'trending':
            # Trending: Recent content with high engagement
            base_query += """
                ORDER BY 
                    CASE WHEN c.Created_At >= DATE_SUB(NOW(), INTERVAL 7 DAY) 
                         THEN (COALESCE(cm.Views, 0) + COALESCE(cm.Likes, 0) * 2) 
                         ELSE 0 END DESC,
                    c.Created_At DESC
            """
        
        # Add limit
        base_query += " LIMIT %s"
        params.append(limit)
        
        cursor.execute(base_query, params)
        recommendations = cursor.fetchall()
        
        # Calculate engagement scores for each recommendation
        for rec in recommendations:
            rec['engagement_score'] = calculate_engagement_score(
                rec['views'], rec['likes'], rec['shares'], rec['comments']
            )
        
        cursor.close()
        connection.close()
        
        return recommendations
        
    except Exception as e:
        print(f"Error getting content recommendations: {e}")
        return []

def get_similar_content(content_id, limit=5):
    """Get content similar to a specific piece of content"""
    try:
        connection = get_db_connection()
        cursor = connection.cursor(dictionary=True)
        
        # First, get the current content's details
        cursor.execute("""
            SELECT c.Content_Type, c.Tags, up.Practice_Area
            FROM Content c
            LEFT JOIN User_Profile up ON c.User_ID = up.User_ID
            WHERE c.Content_ID = %s
        """, (content_id,))
        
        current_content = cursor.fetchone()
        if not current_content:
            return []
        
        # Find similar content based on type, tags, and practice area
        similar_query = """
            SELECT 
                c.Content_ID as content_id,
                c.Title as title,
                c.Summary as summary,
                c.Content_Type as content_type,
                c.Created_At as created_at,
                COALESCE(cm.Views, 0) as views,
                COALESCE(cm.Likes, 0) as likes,
                up.Full_Name as author_name
            FROM Content c
            LEFT JOIN Content_Metrics cm ON c.Content_ID = cm.Content_ID
            LEFT JOIN User_Profile up ON c.User_ID = up.User_ID
            WHERE c.Status = 'Active' 
            AND c.Content_ID != %s
            AND (
                c.Content_Type = %s
                OR up.Practice_Area = %s
            )
            ORDER BY 
                CASE WHEN c.Content_Type = %s THEN 2 ELSE 0 END +
                CASE WHEN up.Practice_Area = %s THEN 1 ELSE 0 END DESC,
                COALESCE(cm.Views, 0) DESC
            LIMIT %s
        """
        
        cursor.execute(similar_query, (
            content_id,
            current_content['Content_Type'],
            current_content['Practice_Area'],
            current_content['Content_Type'],
            current_content['Practice_Area'],
            limit
        ))
        
        similar_content = cursor.fetchall()
        
        cursor.close()
        connection.close()
        
        return similar_content
        
    except Exception as e:
        print(f"Error getting similar content: {e}")
        return []

def get_trending_topics(days=7, limit=10):
    """Get trending topics based on recent content engagement"""
    try:
        connection = get_db_connection()
        cursor = connection.cursor(dictionary=True)
        
        # Get trending topics from tags and practice areas
        cursor.execute("""
            SELECT 
                COALESCE(up.Practice_Area, 'General') as topic,
                COUNT(c.Content_ID) as content_count,
                SUM(COALESCE(cm.Views, 0)) as total_views,
                SUM(COALESCE(cm.Likes, 0)) as total_likes,
                AVG(COALESCE(cm.Views, 0)) as avg_views
            FROM Content c
            LEFT JOIN Content_Metrics cm ON c.Content_ID = cm.Content_ID
            LEFT JOIN User_Profile up ON c.User_ID = up.User_ID
            WHERE c.Status = 'Active'
            AND c.Created_At >= DATE_SUB(NOW(), INTERVAL %s DAY)
            GROUP BY COALESCE(up.Practice_Area, 'General')
            HAVING content_count > 0
            ORDER BY (total_views + total_likes * 2) DESC
            LIMIT %s
        """, (days, limit))
        
        trending_topics = cursor.fetchall()
        
        cursor.close()
        connection.close()
        
        return trending_topics
        
    except Exception as e:
        print(f"Error getting trending topics: {e}")
        return []

@recommendations_bp.route('/api/recommendations/content', methods=['GET'])
@require_permission('content_read_public')
def get_content_recommendations_endpoint(user_id):
    """API endpoint for getting content recommendations"""
    try:
        content_type = request.args.get('type', 'all')
        limit = request.args.get('limit', 10, type=int)
        sort_by = request.args.get('sort_by', 'engagement')
        
        recommendations = get_content_recommendations(
            content_type=content_type,
            user_id=user_id,
            limit=limit,
            sort_by=sort_by
        )
        
        return jsonify({
            'success': True,
            'recommendations': recommendations,
            'total': len(recommendations)
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@recommendations_bp.route('/api/recommendations/similar/<int:content_id>', methods=['GET'])
@require_permission('content_read_public')
def get_similar_content_endpoint(user_id, content_id):
    """API endpoint for getting similar content"""
    try:
        limit = request.args.get('limit', 5, type=int)
        
        similar_content = get_similar_content(content_id, limit)
        
        return jsonify({
            'success': True,
            'similar_content': similar_content,
            'total': len(similar_content)
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@recommendations_bp.route('/api/recommendations/trending', methods=['GET'])
@require_permission('content_read_public')
def get_trending_topics_endpoint(user_id):
    """API endpoint for getting trending topics"""
    try:
        days = request.args.get('days', 7, type=int)
        limit = request.args.get('limit', 10, type=int)
        
        trending_topics = get_trending_topics(days, limit)
        
        return jsonify({
            'success': True,
            'trending_topics': trending_topics,
            'total': len(trending_topics)
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
