from flask import Blueprint, request, jsonify
from datetime import datetime
import bcrypt
from utils import get_db_connection, hash_password, require_permission

# Create Blueprint for admin routes
admin_bp = Blueprint('admin', __name__)

@admin_bp.route('/request_editor_access', methods=['POST'])
def request_editor_access():
    """Request editor access endpoint"""
    data = request.get_json()
    user_id = data['user_id']

    conn = get_db_connection()
    cursor = conn.cursor()

    try:
        cursor.execute("SELECT COUNT(*) FROM Access_Request WHERE User_ID = %s AND Status = 'Pending'", (user_id,))
        count = cursor.fetchone()[0]

        if count > 0:
            return jsonify({'error': 'You already have a pending request.'}), 400

        cursor.execute("""
            INSERT INTO Access_Request (User_ID, Status)
            VALUES (%s, 'Pending')
        """, (user_id,))

        # Get user info for notification
        cursor.execute("""
            SELECT up.Full_Name, up.Practice_Area
            FROM User_Profile up
            WHERE up.User_ID = %s
        """, (user_id,))

        user_info = cursor.fetchone()

        # Create notifications for all admins about new editor access request
        cursor.execute("""
            SELECT User_ID FROM Users WHERE Role_ID = 1
        """)

        admins = cursor.fetchall()

        for admin in admins:
            notification_title = "New Editor Access Request"
            notification_message = f"{user_info[0] if user_info else 'A user'} has requested editor access"
            if user_info and user_info[1]:
                notification_message += f" (Practice Area: {user_info[1]})"
            action_url = "/admin/access-requests"

            cursor.execute("""
                INSERT INTO Notifications (User_ID, Type, Title, Message, Action_URL)
                VALUES (%s, %s, %s, %s, %s)
            """, (admin[0], 'access_request', notification_title, notification_message, action_url))

        conn.commit()
        return jsonify({'message': 'Request for editor access sent to admin.'}), 200
    except Exception as e:
        conn.rollback()
        return jsonify({'error': str(e)}), 500
    finally:
        cursor.close()
        conn.close()

@admin_bp.route('/admin/approve_deny_access', methods=['POST', 'OPTIONS'])
def admin_approve_deny_access():
    """Admin approve/deny access request endpoint"""
    # Handle preflight OPTIONS request
    if request.method == 'OPTIONS':
        return '', 200

    data = request.get_json()
    request_id = data.get('request_id')
    action = data.get('action')  # 'Approve' or 'Deny'
    admin_id = data.get('admin_id')

    # Validate input data
    if not request_id or not action or not admin_id:
        return jsonify({'error': 'Missing required parameters'}), 400

    if action not in ['Approve', 'Deny']:
        return jsonify({'error': 'Invalid action. Must be "Approve" or "Deny"'}), 400

    try:
        # Convert request_id and admin_id to integers if they're strings
        request_id = int(request_id)
        admin_id = int(admin_id)
    except ValueError:
        return jsonify({'error': 'Invalid request_id or admin_id format'}), 400

    conn = get_db_connection()
    cursor = conn.cursor()

    try:
        cursor.execute("SELECT User_ID, Status FROM Access_Request WHERE Request_ID = %s", (request_id,))
        request_data = cursor.fetchone()

        if not request_data:
            return jsonify({'error': 'Request not found'}), 404

        if request_data[1] != 'Pending':
            return jsonify({'error': 'This request has already been processed'}), 400

        user_id = request_data[0]

        if action == 'Approve':
            cursor.execute("""
                UPDATE Access_Request
                SET Status = 'Approved', Approved_At = NOW(), Admin_ID = %s
                WHERE Request_ID = %s
            """, (admin_id, request_id))

            cursor.execute("UPDATE Users SET Role_ID = 2 WHERE User_ID = %s", (user_id,))  # Set role to Editor

            # Log the admin action
            cursor.execute("""
                INSERT INTO Audit_Logs (Admin_ID, Action_Type, Action_Details)
                VALUES (%s, %s, %s)
            """, (admin_id, 'Approve Editor Access', f'Approved editor access for user {user_id}'))

            # Create notification for user about approval
            cursor.execute("""
                INSERT INTO Notifications (User_ID, Type, Title, Message, Action_URL)
                VALUES (%s, %s, %s, %s, %s)
            """, (user_id, 'access_approved', 'Editor Access Approved',
                  'Congratulations! Your request for editor access has been approved. You can now create and manage content.',
                  '/editor-dashboard'))

            message = 'Editor access granted.'
        else:
            cursor.execute("""
                UPDATE Access_Request
                SET Status = 'Denied', Denied_At = NOW(), Admin_ID = %s
                WHERE Request_ID = %s
            """, (admin_id, request_id))

            # Log the admin action
            cursor.execute("""
                INSERT INTO Audit_Logs (Admin_ID, Action_Type, Action_Details)
                VALUES (%s, %s, %s)
            """, (admin_id, 'Deny Editor Access', f'Denied editor access for user {user_id}'))

            # Create notification for user about denial
            cursor.execute("""
                INSERT INTO Notifications (User_ID, Type, Title, Message, Action_URL)
                VALUES (%s, %s, %s, %s, %s)
            """, (user_id, 'access_denied', 'Editor Access Request Denied',
                  'Your request for editor access has been denied. Please contact support if you have questions.',
                  '/profile'))

            message = 'Editor access denied.'

        conn.commit()
        return jsonify({'message': message, 'success': True}), 200
    except Exception as e:
        conn.rollback()
        print(f"Error in approve/deny access: {str(e)}")
        return jsonify({'error': str(e)}), 500
    finally:
        cursor.close()
        conn.close()

@admin_bp.route('/admin/access_requests', methods=['GET'])
def get_access_requests():
    """Get pending access requests endpoint"""
    conn = get_db_connection()
    cursor = conn.cursor(buffered=True)

    try:
        cursor.execute("""
            SELECT ar.Request_ID, ar.User_ID, up.Full_Name, up.Practice_Area,
                   ar.Requested_At, ar.Status
            FROM Access_Request ar
            JOIN User_Profile up ON ar.User_ID = up.User_ID
            WHERE ar.Status = 'Pending'
            ORDER BY ar.Requested_At DESC
        """)

        requests = cursor.fetchall()

        access_requests = []
        for req in requests:
            access_requests.append({
                'request_id': req[0],
                'user_id': req[1],
                'full_name': req[2],
                'practice_area': req[3],
                'requested_at': req[4].isoformat() if req[4] else None,
                'status': req[5]
            })

        return jsonify({'access_requests': access_requests}), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        cursor.close()
        conn.close()

@admin_bp.route('/admin/users', methods=['GET'])
def get_all_users():
    """Get all users endpoint"""
    conn = get_db_connection()
    cursor = conn.cursor(buffered=True)

    try:
        cursor.execute("""
            SELECT u.User_ID, u.Email, u.Role_ID, u.Status, u.Created_At,
                   up.Full_Name, up.Phone, up.Bio, up.Practice_Area, up.Location, up.Years_of_Experience,
                   r.Role_Name,
                   (SELECT COUNT(*) FROM Session s WHERE s.User_ID = u.User_ID AND
                    s.Last_Active_Timestamp > DATE_SUB(NOW(), INTERVAL 30 DAY)) as is_active
            FROM Users u
            LEFT JOIN User_Profile up ON u.User_ID = up.User_ID
            LEFT JOIN Roles r ON u.Role_ID = r.Role_ID
            ORDER BY u.Created_At DESC
        """)

        users = cursor.fetchall()

        user_list = []
        for user in users:
            user_list.append({
                'user_id': user[0],
                'email': user[1],
                'role_id': user[2],
                'status': user[3],
                'created_at': user[4].isoformat() if user[4] else None,
                'full_name': user[5] or '',
                'phone': user[6] or '',
                'bio': user[7] or '',
                'practice_area': user[8] or '',
                'location': user[9] or '',
                'years_of_experience': user[10] or 0,
                'role_name': user[11] or 'User',
                'is_active': bool(user[12])
            })

        return jsonify({'users': user_list}), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        cursor.close()
        conn.close()

@admin_bp.route('/admin/analytics', methods=['GET'])
def get_admin_analytics():
    """Get admin analytics endpoint"""
    conn = get_db_connection()
    cursor = conn.cursor(buffered=True)

    try:
        # Get user counts by role
        cursor.execute("""
            SELECT r.Role_Name, COUNT(u.User_ID) as count
            FROM Roles r
            LEFT JOIN Users u ON r.Role_ID = u.Role_ID AND u.Status = 'Active'
            GROUP BY r.Role_ID, r.Role_Name
        """)
        role_counts = cursor.fetchall()

        # Get active users (logged in within last 30 days)
        cursor.execute("""
            SELECT COUNT(DISTINCT s.User_ID) as active_users
            FROM Session s
            WHERE s.Last_Active_Timestamp > DATE_SUB(NOW(), INTERVAL 30 DAY)
        """)
        active_users = cursor.fetchone()[0]

        # Get total users
        cursor.execute("SELECT COUNT(*) FROM Users WHERE Status = 'Active'")
        total_users = cursor.fetchone()[0]

        # Get pending access requests
        cursor.execute("SELECT COUNT(*) FROM Access_Request WHERE Status = 'Pending'")
        pending_requests = cursor.fetchone()[0]

        # Get user registrations by month (last 6 months)
        cursor.execute("""
            SELECT DATE_FORMAT(Created_At, '%Y-%m') as month, COUNT(*) as count
            FROM Users
            WHERE Created_At >= DATE_SUB(NOW(), INTERVAL 6 MONTH)
            GROUP BY DATE_FORMAT(Created_At, '%Y-%m')
            ORDER BY month
        """)
        monthly_registrations = cursor.fetchall()

        analytics = {
            'role_counts': [{'role': role[0], 'count': role[1]} for role in role_counts],
            'active_users': active_users,
            'total_users': total_users,
            'pending_requests': pending_requests,
            'monthly_registrations': [{'month': reg[0], 'count': reg[1]} for reg in monthly_registrations]
        }

        return jsonify(analytics), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        cursor.close()
        conn.close()

@admin_bp.route('/admin/audit_logs', methods=['GET'])
def get_audit_logs():
    """Get audit logs endpoint"""
    conn = get_db_connection()
    cursor = conn.cursor(buffered=True)

    try:
        cursor.execute("""
            SELECT al.Log_ID, al.Admin_ID, al.Action_Type, al.Action_Details, al.Timestamp,
                   up.Full_Name as admin_name
            FROM Audit_Logs al
            LEFT JOIN User_Profile up ON al.Admin_ID = up.User_ID
            ORDER BY al.Timestamp DESC
            LIMIT 100
        """)

        logs = cursor.fetchall()

        audit_logs = []
        for log in logs:
            audit_logs.append({
                'log_id': log[0],
                'admin_id': log[1],
                'action_type': log[2],
                'action_details': log[3],
                'timestamp': log[4].isoformat() if log[4] else None,
                'admin_name': log[5] or 'Unknown Admin'
            })

        return jsonify({'audit_logs': audit_logs}), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        cursor.close()
        conn.close()

@admin_bp.route('/admin/users/<int:user_id>', methods=['PUT'])
@require_permission('admin_manage_users')
def update_user_role(admin_id, user_id):
    """Update user role endpoint"""
    data = request.get_json()
    new_role_id = data.get('role_id')

    if not new_role_id:
        return jsonify({'error': 'Role ID is required'}), 400

    conn = get_db_connection()
    cursor = conn.cursor()

    try:
        # Check if user exists
        cursor.execute("SELECT User_ID, Role_ID FROM Users WHERE User_ID = %s", (user_id,))
        user = cursor.fetchone()

        if not user:
            return jsonify({'error': 'User not found'}), 404

        old_role_id = user[1]

        # Update user role
        cursor.execute("UPDATE Users SET Role_ID = %s WHERE User_ID = %s", (new_role_id, user_id))

        # Log the admin action
        cursor.execute("""
            INSERT INTO Audit_Logs (Admin_ID, Action_Type, Action_Details)
            VALUES (%s, %s, %s)
        """, (admin_id, 'Update User Role', f'Changed user {user_id} role from {old_role_id} to {new_role_id}'))

        conn.commit()
        return jsonify({'message': 'User role updated successfully'}), 200
    except Exception as e:
        conn.rollback()
        return jsonify({'error': str(e)}), 500
    finally:
        cursor.close()
        conn.close()

@admin_bp.route('/admin/users/<int:user_id>/status', methods=['PUT'])
@require_permission('admin_manage_users')
def update_user_status(admin_id, user_id):
    """Update user status endpoint"""
    data = request.get_json()
    new_status = data.get('status')

    if not new_status or new_status not in ['Active', 'Inactive', 'Suspended']:
        return jsonify({'error': 'Valid status is required (Active, Inactive, Suspended)'}), 400

    conn = get_db_connection()
    cursor = conn.cursor()

    try:
        # Check if user exists
        cursor.execute("SELECT User_ID, Status FROM Users WHERE User_ID = %s", (user_id,))
        user = cursor.fetchone()

        if not user:
            return jsonify({'error': 'User not found'}), 404

        old_status = user[1]

        # Update user status
        cursor.execute("UPDATE Users SET Status = %s WHERE User_ID = %s", (new_status, user_id))

        # Log the admin action
        cursor.execute("""
            INSERT INTO Audit_Logs (Admin_ID, Action_Type, Action_Details)
            VALUES (%s, %s, %s)
        """, (admin_id, 'Update User Status', f'Changed user {user_id} status from {old_status} to {new_status}'))

        # If user is suspended or deactivated, remove their sessions
        if new_status in ['Inactive', 'Suspended']:
            cursor.execute("DELETE FROM Session WHERE User_ID = %s", (user_id,))

        conn.commit()
        return jsonify({'message': 'User status updated successfully'}), 200
    except Exception as e:
        conn.rollback()
        return jsonify({'error': str(e)}), 500
    finally:
        cursor.close()
        conn.close()

@admin_bp.route('/admin/content/moderate', methods=['GET'])
@require_permission('admin_moderate_content')
def get_content_for_moderation(admin_id):
    """Get content that needs moderation"""
    conn = get_db_connection()
    cursor = conn.cursor(buffered=True)

    try:
        # Get content that might need moderation (recently created, reported, etc.)
        cursor.execute("""
            SELECT c.Content_ID, c.Title, c.Content_Type, c.Status, c.Created_At,
                   up.Full_Name as author_name, up.User_ID as author_id,
                   COALESCE(cm.Views, 0) as views,
                   COALESCE(cm.Likes, 0) as likes,
                   COALESCE(cm.Comments_Count, 0) as comments
            FROM Content c
            LEFT JOIN User_Profile up ON c.User_ID = up.User_ID
            LEFT JOIN Content_Metrics cm ON c.Content_ID = cm.Content_ID
            WHERE c.Status IN ('Active', 'Pending')
            ORDER BY c.Created_At DESC
            LIMIT 50
        """)

        content = cursor.fetchall()

        content_list = []
        for item in content:
            content_list.append({
                'content_id': item[0],
                'title': item[1],
                'content_type': item[2],
                'status': item[3],
                'created_at': item[4].isoformat() if item[4] else None,
                'author_name': item[5] or 'Unknown',
                'author_id': item[6],
                'views': item[7],
                'likes': item[8],
                'comments': item[9]
            })

        return jsonify({'content': content_list}), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        cursor.close()
        conn.close()

@admin_bp.route('/admin/content/<int:content_id>/moderate', methods=['PUT'])
@require_permission('admin_moderate_content')
def moderate_content(admin_id, content_id):
    """Moderate content (approve, reject, etc.)"""
    data = request.get_json()
    action = data.get('action')  # 'approve', 'reject', 'suspend'
    reason = data.get('reason', '')

    if not action or action not in ['approve', 'reject', 'suspend']:
        return jsonify({'error': 'Valid action is required (approve, reject, suspend)'}), 400

    conn = get_db_connection()
    cursor = conn.cursor()

    try:
        # Check if content exists
        cursor.execute("SELECT Content_ID, Status, User_ID FROM Content WHERE Content_ID = %s", (content_id,))
        content = cursor.fetchone()

        if not content:
            return jsonify({'error': 'Content not found'}), 404

        old_status = content[1]
        content_owner_id = content[2]

        # Update content status based on action
        if action == 'approve':
            new_status = 'Active'
        elif action == 'reject':
            new_status = 'Rejected'
        else:  # suspend
            new_status = 'Suspended'

        cursor.execute("UPDATE Content SET Status = %s WHERE Content_ID = %s", (new_status, content_id))

        # Log the admin action
        action_details = f'Moderated content {content_id}: {old_status} -> {new_status}'
        if reason:
            action_details += f' (Reason: {reason})'

        cursor.execute("""
            INSERT INTO Audit_Logs (Admin_ID, Action_Type, Action_Details)
            VALUES (%s, %s, %s)
        """, (admin_id, 'Moderate Content', action_details))

        # Notify content owner if content was rejected or suspended
        if action in ['reject', 'suspend']:
            notification_title = f'Content {action.title()}d'
            notification_message = f'Your content has been {action}d by an administrator.'
            if reason:
                notification_message += f' Reason: {reason}'

            cursor.execute("""
                INSERT INTO Notifications (User_ID, Type, Title, Message)
                VALUES (%s, %s, %s, %s)
            """, (content_owner_id, 'content_moderated', notification_title, notification_message))

        conn.commit()
        return jsonify({'message': f'Content {action}d successfully'}), 200
    except Exception as e:
        conn.rollback()
        return jsonify({'error': str(e)}), 500
    finally:
        cursor.close()
        conn.close()

@admin_bp.route('/admin/system/stats', methods=['GET'])
@require_permission('admin_view_system')
def get_system_stats(admin_id):
    """Get system statistics"""
    conn = get_db_connection()
    cursor = conn.cursor(buffered=True)

    try:
        stats = {}

        # Content statistics
        cursor.execute("""
            SELECT Content_Type, COUNT(*) as count
            FROM Content
            WHERE Status = 'Active'
            GROUP BY Content_Type
        """)
        content_stats = cursor.fetchall()
        stats['content_by_type'] = [{'type': stat[0], 'count': stat[1]} for stat in content_stats]

        # User activity statistics
        cursor.execute("""
            SELECT
                COUNT(DISTINCT CASE WHEN s.Last_Active_Timestamp > DATE_SUB(NOW(), INTERVAL 1 DAY) THEN s.User_ID END) as daily_active,
                COUNT(DISTINCT CASE WHEN s.Last_Active_Timestamp > DATE_SUB(NOW(), INTERVAL 7 DAY) THEN s.User_ID END) as weekly_active,
                COUNT(DISTINCT CASE WHEN s.Last_Active_Timestamp > DATE_SUB(NOW(), INTERVAL 30 DAY) THEN s.User_ID END) as monthly_active
            FROM Session s
        """)
        activity_stats = cursor.fetchone()
        stats['user_activity'] = {
            'daily_active': activity_stats[0],
            'weekly_active': activity_stats[1],
            'monthly_active': activity_stats[2]
        }

        # Content engagement statistics
        cursor.execute("""
            SELECT
                SUM(Views) as total_views,
                SUM(Likes) as total_likes,
                SUM(Shares) as total_shares,
                AVG(Views) as avg_views_per_content
            FROM Content_Metrics
        """)
        engagement_stats = cursor.fetchone()
        stats['engagement'] = {
            'total_views': engagement_stats[0] or 0,
            'total_likes': engagement_stats[1] or 0,
            'total_shares': engagement_stats[2] or 0,
            'avg_views_per_content': float(engagement_stats[3]) if engagement_stats[3] else 0
        }

        return jsonify({'stats': stats}), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        cursor.close()
        conn.close()
