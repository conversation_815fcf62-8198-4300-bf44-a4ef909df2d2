import os
import bcrypt
import uuid
import json
from datetime import datetime, date
from mysql.connector import pooling
from functools import wraps
from flask import request, jsonify
from werkzeug.utils import secure_filename

# MySQL Connection Pool Configuration
db_config = {
    'host': os.getenv('DB_HOST', 'localhost'),
    'user': os.getenv('DB_USER', 'root'),
    'password': os.getenv('DB_PASSWORD', 'pabbo@123'),
    'database': os.getenv('DB_NAME', 'lawfort'),
    'pool_name': 'lawfort_pool',
    'pool_size': int(os.getenv('DB_POOL_SIZE', 5))
}

# Create connection pool
connection_pool = pooling.MySQLConnectionPool(**db_config)

# File Upload Configuration
UPLOAD_FOLDER = os.path.join(os.getcwd(), 'uploads', 'resumes')
ALLOWED_EXTENSIONS = {'pdf'}
MAX_FILE_SIZE = 5 * 1024 * 1024  # 5MB

# Ensure upload directory exists
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

def allowed_file(filename):
    """Check if file extension is allowed"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def get_db_connection():
    """Get database connection from pool"""
    return connection_pool.get_connection()

def hash_password(password):
    """Hash password using bcrypt"""
    return bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt())

def check_password(stored_password, entered_password):
    """Check password against stored hash"""
    # Convert stored_password to bytes if it's a string
    if isinstance(stored_password, str):
        stored_password = stored_password.encode('utf-8')
    return bcrypt.checkpw(entered_password.encode('utf-8'), stored_password)

def generate_session_token():
    """Generate unique session token"""
    return str(uuid.uuid4())

class CustomJSONEncoder(json.JSONEncoder):
    """JSON encoder to handle date objects"""
    def default(self, obj):
        if isinstance(obj, (datetime, date)):
            return obj.isoformat()
        return super().default(obj)

def check_user_permission(user_id, permission_name, content_id=None, content_owner_id=None):
    """Check user permissions with hierarchical support"""
    try:
        connection = get_db_connection()
        cursor = connection.cursor(dictionary=True)

        # Get user's role and super admin status
        cursor.execute("""
            SELECT u.Role_ID, u.Is_Super_Admin
            FROM Users u
            WHERE u.User_ID = %s
        """, (user_id,))

        user_info = cursor.fetchone()
        if not user_info:
            cursor.close()
            connection.close()
            return False

        # Super admins bypass all permission checks
        if user_info['Is_Super_Admin']:
            cursor.close()
            connection.close()
            return True

        role_id = user_info['Role_ID']

        # Check for exact permission match
        cursor.execute("""
            SELECT p.Permission_Name
            FROM Permissions p
            WHERE p.Role_ID = %s AND p.Permission_Name = %s
        """, (role_id, permission_name))

        exact_match = cursor.fetchone()

        if exact_match:
            cursor.close()
            connection.close()
            return True

        # Check for hierarchical permissions based on role and content ownership
        if permission_name.endswith('_own') and content_owner_id:
            # For "own" permissions, check if user owns the content
            if user_id == content_owner_id:
                cursor.close()
                connection.close()
                return True

        # Check for broader permissions that include the requested permission
        permission_hierarchy = {
            # Admin permissions (role_id = 1) include all
            'content_create_all': ['content_create_own', 'content_create'],
            'content_read_all': ['content_read_public', 'content_read'],
            'content_update_all': ['content_update_own', 'content_update'],
            'content_delete_all': ['content_delete_own', 'content_delete'],
            'metrics_view_all': ['metrics_view_own', 'metrics_view'],

            # Editor permissions (role_id = 2) for their own content
            'content_create_own': ['content_create'],
            'content_update_own': ['content_update'],
            'content_delete_own': ['content_delete'],
            'content_publish_own': ['content_publish'],
            'metrics_view_own': ['metrics_view'],
        }

        # Check if user has a broader permission that includes the requested one
        for broad_perm, included_perms in permission_hierarchy.items():
            if permission_name in included_perms:
                cursor.execute("""
                    SELECT p.Permission_Name
                    FROM Permissions p
                    WHERE p.Role_ID = %s AND p.Permission_Name = %s
                """, (role_id, broad_perm))

                broader_match = cursor.fetchone()

                if broader_match:
                    cursor.close()
                    connection.close()
                    return True

        cursor.close()
        connection.close()
        return False
    except Exception as e:
        return False

def get_content_owner(content_id):
    """Get content owner ID"""
    try:
        connection = get_db_connection()
        cursor = connection.cursor(dictionary=True)

        cursor.execute("SELECT User_ID FROM Content WHERE Content_ID = %s", (content_id,))
        result = cursor.fetchone()

        cursor.close()
        connection.close()

        return result['User_ID'] if result else None
    except Exception as e:
        return None

def require_permission(permission_name, check_ownership=False):
    """Enhanced decorator for routes that require specific permissions"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # Get session token from request headers
            session_token = request.headers.get('Authorization')
            if not session_token:
                return jsonify({"success": False, "message": "No session token provided"}), 401

            # Remove 'Bearer ' prefix if present
            if session_token.startswith('Bearer '):
                session_token = session_token[7:]

            try:
                connection = get_db_connection()
                cursor = connection.cursor(dictionary=True)

                # Verify session token and get user ID
                cursor.execute("""
                    SELECT s.User_ID
                    FROM Session s
                    WHERE s.Session_Token = %s
                """, (session_token,))

                session = cursor.fetchone()
                if not session:
                    cursor.close()
                    connection.close()
                    return jsonify({"success": False, "message": "Invalid session token"}), 401

                user_id = session['User_ID']
                cursor.close()
                connection.close()

                # For ownership-based permissions, get content_id from URL parameters
                content_owner_id = None
                if check_ownership and len(args) > 0:
                    # Assume first argument after user_id is content_id
                    content_id = args[0] if args else None
                    if content_id:
                        content_owner_id = get_content_owner(content_id)

                # Check permission with enhanced logic
                if check_user_permission(user_id, permission_name, content_owner_id=content_owner_id):
                    return f(user_id, *args, **kwargs)
                else:
                    return jsonify({"success": False, "message": "Permission denied"}), 403

            except Exception as e:
                return jsonify({"success": False, "message": str(e)}), 500

        return decorated_function
    return decorator

# Email function placeholder (for future implementation)
def send_email(to_emails, subject, content, _sender_id=None):
    """
    Email function placeholder - to be implemented with actual email service
    """
    try:
        # Ensure to_emails is a list
        if isinstance(to_emails, str):
            to_emails = [to_emails]

        # TODO: Implement actual email sending logic
        # For now, return True to maintain compatibility
        return True

    except Exception as e:
        return False

# Email logging placeholder (for future implementation)
def log_email_in_db(sender_id, _recipient_emails, _subject, _content, status):
    """
    Email logging placeholder - to be implemented with actual database logging
    """
    # TODO: Implement actual email logging to database
    return True
